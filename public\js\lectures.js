// ملف JavaScript لصفحة المحاضرات والدروس

// بيانات تجريبية للمحاضرات
const lecturesData = [
    {
        id: 1,
        title: "فقه العبادات في الإسلام",
        lecturer: "د. عب<PERSON> الله المحمود",
        province: "الرياض",
        location: "مسجد الإمام محمد بن سعود",
        day: "الأحد",
        time: "20:00",
        type: "درس",
        description: "درس أسبوعي في فقه العبادات يتناول أحكام الطهارة والصلاة والزكاة",
        contact: "0501234567",
        status: "active"
    },
    {
        id: 2,
        title: "السيرة النبوية الشريفة",
        lecturer: "الشيخ أحمد الغامدي",
        province: "جدة",
        location: "مسجد الملك فهد",
        day: "الثلاثاء",
        time: "19:30",
        type: "محاضرة",
        description: "محاضرة شهرية تتناول أحداث السيرة النبوية وما فيها من دروس وعبر",
        contact: "0509876543",
        status: "active"
    },
    {
        id: 3,
        title: "التربية الإسلامية للأطفال",
        lecturer: "د. فاطمة الزهراني",
        province: "مكة",
        location: "مركز الدعوة والإرشاد",
        day: "الخميس",
        time: "16:00",
        type: "ندوة",
        description: "ندوة تربوية للأمهات حول أساليب التربية الإسلامية الصحيحة",
        contact: "0512345678",
        status: "active"
    },
    {
        id: 4,
        title: "دورة تحفيظ القرآن الكريم",
        lecturer: "الشيخ محمد العتيبي",
        province: "المدينة",
        location: "مسجد قباء",
        day: "السبت",
        time: "17:00",
        type: "دورة",
        description: "دورة تدريبية لتعليم أحكام التجويد وحفظ القرآن الكريم",
        contact: "0556789012",
        status: "active"
    },
    {
        id: 5,
        title: "الأخلاق في الإسلام",
        lecturer: "د. سعد الشهري",
        province: "الدمام",
        location: "مسجد الفاروق",
        day: "الاثنين",
        time: "20:30",
        type: "درس",
        description: "درس أسبوعي يتناول الأخلاق الإسلامية وتطبيقها في الحياة العملية",
        contact: "0543210987",
        status: "active"
    },
    {
        id: 6,
        title: "فقه المعاملات المالية",
        lecturer: "د. خالد الراجحي",
        province: "الطائف",
        location: "مسجد الهدى",
        day: "الأربعاء",
        time: "19:00",
        type: "محاضرة",
        description: "محاضرة تتناول أحكام البيع والشراء والمعاملات المالية في الإسلام",
        contact: "0567890123",
        status: "active"
    }
];

// متغيرات عامة
let currentView = 'table';
let currentPage = 1;
let currentMonth = new Date().getMonth();
let currentYear = new Date().getFullYear();
const lecturesPerPage = 10;
let filteredLectures = [...lecturesData];
let currentUser = null;
let sortColumn = '';
let sortDirection = 'asc';

// عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحقق من حالة تسجيل الدخول
    checkUserLogin();
    
    // عرض المحاضرات
    displayLectures();
    
    // إعداد الترقيم
    setupPagination();
    
    // إضافة مستمعي الأحداث
    setupEventListeners();
    
    // تحميل المحاضرات من التخزين المحلي
    loadLecturesFromStorage();
    
    // إعداد التقويم
    setupCalendar();
});

// تحقق من حالة تسجيل الدخول
function checkUserLogin() {
    const userData = localStorage.getItem('currentUser');
    if (userData) {
        currentUser = JSON.parse(userData);
        // إظهار زر إضافة محاضرة للأعضاء
        if (currentUser.role === 'member' || currentUser.role === 'scholar') {
            document.getElementById('add-lecture-btn').style.display = 'block';
        }
    }
}

// تحميل المحاضرات من التخزين المحلي
function loadLecturesFromStorage() {
    const storedLectures = localStorage.getItem('lectures');
    if (storedLectures) {
        const lectures = JSON.parse(storedLectures);
        lecturesData.push(...lectures);
        filteredLectures = [...lecturesData];
        displayLectures();
        setupPagination();
    } else {
        // حفظ البيانات التجريبية في التخزين المحلي
        localStorage.setItem('lectures', JSON.stringify(lecturesData));
    }
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // البحث
    const searchInput = document.getElementById('search-input');
    searchInput.addEventListener('input', debounce(filterLectures, 300));
    
    // الفلترة
    const provinceFilter = document.getElementById('province-filter');
    const dayFilter = document.getElementById('day-filter');
    const typeFilter = document.getElementById('type-filter');
    
    provinceFilter.addEventListener('change', filterLectures);
    dayFilter.addEventListener('change', filterLectures);
    typeFilter.addEventListener('change', filterLectures);
    
    // أزرار العرض
    const viewButtons = document.querySelectorAll('.view-btn');
    viewButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const view = this.getAttribute('data-view');
            switchView(view);
        });
    });
    
    // زر إضافة محاضرة
    const addLectureBtn = document.getElementById('add-lecture-btn');
    if (addLectureBtn) {
        addLectureBtn.addEventListener('click', openAddLectureModal);
    }
    
    // إغلاق النافذة المنبثقة
    const closeModal = document.getElementById('close-modal');
    const cancelBtn = document.getElementById('cancel-lecture');
    const modal = document.getElementById('add-lecture-modal');
    
    closeModal.addEventListener('click', closeAddLectureModal);
    cancelBtn.addEventListener('click', closeAddLectureModal);
    
    // إغلاق النافذة عند النقر خارجها
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeAddLectureModal();
        }
    });
    
    // نموذج إضافة محاضرة
    const addLectureForm = document.getElementById('add-lecture-form');
    addLectureForm.addEventListener('submit', handleAddLecture);
    
    // التقويم
    document.getElementById('prev-month').addEventListener('click', () => {
        currentMonth--;
        if (currentMonth < 0) {
            currentMonth = 11;
            currentYear--;
        }
        setupCalendar();
    });
    
    document.getElementById('next-month').addEventListener('click', () => {
        currentMonth++;
        if (currentMonth > 11) {
            currentMonth = 0;
            currentYear++;
        }
        setupCalendar();
    });
}

// فلترة المحاضرات
function filterLectures() {
    const searchTerm = document.getElementById('search-input').value.toLowerCase();
    const provinceFilter = document.getElementById('province-filter').value;
    const dayFilter = document.getElementById('day-filter').value;
    const typeFilter = document.getElementById('type-filter').value;
    
    filteredLectures = lecturesData.filter(lecture => {
        const matchesSearch = lecture.title.toLowerCase().includes(searchTerm) ||
                            lecture.lecturer.toLowerCase().includes(searchTerm) ||
                            lecture.location.toLowerCase().includes(searchTerm);
        
        const matchesProvince = !provinceFilter || lecture.province === provinceFilter;
        const matchesDay = !dayFilter || lecture.day === dayFilter;
        const matchesType = !typeFilter || lecture.type === typeFilter;
        
        return matchesSearch && matchesProvince && matchesDay && matchesType && lecture.status === 'active';
    });
    
    currentPage = 1;
    displayLectures();
    setupPagination();
}

// تبديل العرض
function switchView(view) {
    currentView = view;
    
    // تحديث أزرار العرض
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`).classList.add('active');
    
    // إخفاء جميع العروض
    document.getElementById('table-view').style.display = 'none';
    document.getElementById('cards-view').style.display = 'none';
    document.getElementById('calendar-view').style.display = 'none';
    
    // إظهار العرض المحدد
    switch(view) {
        case 'table':
            document.getElementById('table-view').style.display = 'block';
            displayTableView();
            break;
        case 'cards':
            document.getElementById('cards-view').style.display = 'block';
            displayCardsView();
            break;
        case 'calendar':
            document.getElementById('calendar-view').style.display = 'block';
            setupCalendar();
            break;
    }
}

// عرض المحاضرات
function displayLectures() {
    if (currentView === 'table') {
        displayTableView();
    } else if (currentView === 'cards') {
        displayCardsView();
    }
    
    // إظهار/إخفاء رسالة عدم وجود نتائج
    const noResults = document.getElementById('no-results');
    if (filteredLectures.length === 0) {
        noResults.style.display = 'block';
    } else {
        noResults.style.display = 'none';
    }
}

// عرض الجدول
function displayTableView() {
    const tableBody = document.getElementById('lectures-table-body');
    
    if (filteredLectures.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="7">لا توجد محاضرات</td></tr>';
        return;
    }
    
    const startIndex = (currentPage - 1) * lecturesPerPage;
    const endIndex = startIndex + lecturesPerPage;
    const lecturesToShow = filteredLectures.slice(startIndex, endIndex);
    
    tableBody.innerHTML = lecturesToShow.map(lecture => `
        <tr>
            <td>${lecture.province}</td>
            <td>${lecture.lecturer}</td>
            <td>${lecture.location}</td>
            <td>${lecture.day}</td>
            <td>${formatTime(lecture.time)}</td>
            <td><span class="lecture-type-badge ${lecture.type}">${lecture.type}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn view" onclick="viewLecture(${lecture.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit" onclick="editLecture(${lecture.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete" onclick="deleteLecture(${lecture.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// عرض البطاقات
function displayCardsView() {
    const lecturesGrid = document.getElementById('lectures-grid');
    
    if (filteredLectures.length === 0) {
        lecturesGrid.innerHTML = '<p>لا توجد محاضرات</p>';
        return;
    }
    
    const startIndex = (currentPage - 1) * lecturesPerPage;
    const endIndex = startIndex + lecturesPerPage;
    const lecturesToShow = filteredLectures.slice(startIndex, endIndex);
    
    lecturesGrid.innerHTML = lecturesToShow.map(lecture => createLectureCard(lecture)).join('');
}

// إنشاء بطاقة محاضرة
function createLectureCard(lecture) {
    return `
        <div class="lecture-card">
            <div class="lecture-card-header">
                <div class="lecture-type">${lecture.type}</div>
                <h3 class="lecture-title">${lecture.title}</h3>
                <div class="lecture-lecturer">
                    <i class="fas fa-user"></i>
                    ${lecture.lecturer}
                </div>
            </div>
            <div class="lecture-card-body">
                <div class="lecture-info">
                    <div class="info-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>${lecture.province}</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-mosque"></i>
                        <span>${lecture.location}</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-calendar"></i>
                        <span>${lecture.day}</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-clock"></i>
                        <span>${formatTime(lecture.time)}</span>
                    </div>
                </div>
                ${lecture.description ? `<p class="lecture-description">${lecture.description}</p>` : ''}
            </div>
            <div class="lecture-card-footer">
                ${lecture.contact ? `<div class="contact-info"><i class="fas fa-phone"></i> ${lecture.contact}</div>` : ''}
                <div class="card-actions">
                    <button class="action-btn view" onclick="viewLecture(${lecture.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit" onclick="editLecture(${lecture.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete" onclick="deleteLecture(${lecture.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
}

// إعداد التقويم
function setupCalendar() {
    const monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    
    document.getElementById('calendar-title').textContent = `${monthNames[currentMonth]} ${currentYear}`;
    
    const calendarGrid = document.getElementById('calendar-grid');
    const firstDay = new Date(currentYear, currentMonth, 1);
    const lastDay = new Date(currentYear, currentMonth + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();
    
    let calendarHTML = '';
    
    // أيام الأسبوع
    const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    dayNames.forEach(day => {
        calendarHTML += `<div class="calendar-day-header">${day}</div>`;
    });
    
    // الأيام الفارغة في بداية الشهر
    for (let i = 0; i < startingDayOfWeek; i++) {
        calendarHTML += '<div class="calendar-day other-month"></div>';
    }
    
    // أيام الشهر
    for (let day = 1; day <= daysInMonth; day++) {
        const isToday = new Date().getDate() === day && 
                       new Date().getMonth() === currentMonth && 
                       new Date().getFullYear() === currentYear;
        
        const dayEvents = getLecturesForDay(day);
        
        calendarHTML += `
            <div class="calendar-day ${isToday ? 'today' : ''}">
                <div class="day-number">${day}</div>
                <div class="day-events">
                    ${dayEvents.map(event => `<div class="event-item">${event.title}</div>`).join('')}
                </div>
            </div>
        `;
    }
    
    calendarGrid.innerHTML = calendarHTML;
}

// الحصول على المحاضرات لليوم المحدد
function getLecturesForDay(day) {
    const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    const date = new Date(currentYear, currentMonth, day);
    const dayName = dayNames[date.getDay()];
    
    return filteredLectures.filter(lecture => lecture.day === dayName);
}

// ترتيب الجدول
function sortTable(column) {
    if (sortColumn === column) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn = column;
        sortDirection = 'asc';
    }
    
    filteredLectures.sort((a, b) => {
        let aValue = a[column];
        let bValue = b[column];
        
        if (column === 'time') {
            aValue = convertTimeToMinutes(aValue);
            bValue = convertTimeToMinutes(bValue);
        }
        
        if (sortDirection === 'asc') {
            return aValue > bValue ? 1 : -1;
        } else {
            return aValue < bValue ? 1 : -1;
        }
    });
    
    displayLectures();
}

// تحويل الوقت إلى دقائق
function convertTimeToMinutes(time) {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
}

// إعداد الترقيم
function setupPagination() {
    const pagination = document.getElementById('pagination');
    const totalPages = Math.ceil(filteredLectures.length / lecturesPerPage);
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // زر السابق
    paginationHTML += `
        <button ${currentPage === 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">
            <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    // أرقام الصفحات
    for (let i = 1; i <= totalPages; i++) {
        if (i === currentPage) {
            paginationHTML += `<button class="active">${i}</button>`;
        } else {
            paginationHTML += `<button onclick="changePage(${i})">${i}</button>`;
        }
    }
    
    // زر التالي
    paginationHTML += `
        <button ${currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">
            <i class="fas fa-chevron-left"></i>
        </button>
    `;
    
    pagination.innerHTML = paginationHTML;
}

// تغيير الصفحة
function changePage(page) {
    currentPage = page;
    displayLectures();
    setupPagination();
    
    // التمرير إلى أعلى الصفحة
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// فتح نافذة إضافة محاضرة
function openAddLectureModal() {
    if (!currentUser) {
        alert('يجب تسجيل الدخول أولاً');
        window.location.href = 'login.html';
        return;
    }
    
    const modal = document.getElementById('add-lecture-modal');
    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
}

// إغلاق نافذة إضافة محاضرة
function closeAddLectureModal() {
    const modal = document.getElementById('add-lecture-modal');
    modal.classList.remove('active');
    document.body.style.overflow = 'auto';
    
    // إعادة تعيين النموذج
    document.getElementById('add-lecture-form').reset();
}

// معالجة إضافة محاضرة جديدة
function handleAddLecture(e) {
    e.preventDefault();
    
    const title = document.getElementById('lecture-title').value;
    const type = document.getElementById('lecture-type').value;
    const lecturer = document.getElementById('lecture-lecturer').value;
    const province = document.getElementById('lecture-province').value;
    const location = document.getElementById('lecture-location').value;
    const day = document.getElementById('lecture-day').value;
    const time = document.getElementById('lecture-time').value;
    const description = document.getElementById('lecture-description').value;
    const contact = document.getElementById('lecture-contact').value;
    
    if (!title || !type || !lecturer || !province || !location || !day || !time) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    const newLecture = {
        id: Date.now(),
        title: title,
        type: type,
        lecturer: lecturer,
        province: province,
        location: location,
        day: day,
        time: time,
        description: description,
        contact: contact,
        status: 'pending' // يحتاج موافقة الإدارة
    };
    
    // إضافة المحاضرة إلى البيانات
    lecturesData.push(newLecture);
    
    // حفظ في التخزين المحلي
    const storedLectures = JSON.parse(localStorage.getItem('lectures')) || [];
    storedLectures.push(newLecture);
    localStorage.setItem('lectures', JSON.stringify(storedLectures));
    
    // إغلاق النافذة
    closeAddLectureModal();
    
    // إظهار رسالة نجاح
    alert('تم إرسال المحاضرة بنجاح! سيتم مراجعتها من قبل الإدارة قبل النشر.');
}

// عرض تفاصيل المحاضرة
function viewLecture(id) {
    const lecture = lecturesData.find(l => l.id === id);
    if (lecture) {
        alert(`تفاصيل المحاضرة:\n\nالعنوان: ${lecture.title}\nالمحاضر: ${lecture.lecturer}\nالمكان: ${lecture.location}\nالوقت: ${lecture.day} - ${formatTime(lecture.time)}\nالوصف: ${lecture.description || 'غير متوفر'}`);
    }
}

// تعديل المحاضرة
function editLecture(id) {
    if (!currentUser) {
        alert('يجب تسجيل الدخول أولاً');
        return;
    }
    alert('ميزة التعديل ستكون متاحة قريباً');
}

// حذف المحاضرة
function deleteLecture(id) {
    if (!currentUser) {
        alert('يجب تسجيل الدخول أولاً');
        return;
    }
    
    if (confirm('هل أنت متأكد من حذف هذه المحاضرة؟')) {
        const index = lecturesData.findIndex(l => l.id === id);
        if (index !== -1) {
            lecturesData.splice(index, 1);
            
            // تحديث التخزين المحلي
            localStorage.setItem('lectures', JSON.stringify(lecturesData));
            
            // إعادة تحميل البيانات
            filteredLectures = [...lecturesData];
            displayLectures();
            setupPagination();
            
            alert('تم حذف المحاضرة بنجاح');
        }
    }
}

// تنسيق الوقت
function formatTime(time) {
    const [hours, minutes] = time.split(':');
    const hour12 = hours % 12 || 12;
    const ampm = hours >= 12 ? 'مساءً' : 'صباحاً';
    return `${hour12}:${minutes} ${ampm}`;
}

// دالة تأخير للبحث
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
